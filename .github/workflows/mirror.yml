name: Mirror

on:
  push:
    branches: [main, master]
  workflow_dispatch:

jobs:
  codeberg:
    if: github.repository_owner == 'jiacai2050'
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
        with:
          fetch-depth: 0
      - uses: pixta-dev/repository-mirroring-action@v1
        with:
          target_repo_url: https://${{ secrets.CBTOKEN }}@codeberg.org/${{ github.repository }}.git
