name: CI

on:
  workflow_dispatch:
  pull_request:
    paths:
      - '**.zig'
      - '**.yml'
  push:
    branches:
      - main
    paths:
      - '**.zig'
      - '**.yml'

jobs:
  ci:
    timeout-minutes: 10
    runs-on: ${{ matrix.os }}
    strategy:
      fail-fast: false
      matrix:
        os: [ubuntu-latest, macos-latest]
        zig-version: [0.14.1]
    steps:
      - uses: actions/checkout@v4
      - uses: mlugg/setup-zig@v2
        with:
          version: ${{ matrix.zig-version }}
      - name: Run lint
        run: |
          make lint
      - name: Run tests
        run: |
          make test
