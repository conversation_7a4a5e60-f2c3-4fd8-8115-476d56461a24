# Tests for zig-napi

This directory contains tests for the zig-napi project.

## Test Structure

### Zig Unit Tests
- Located in `examples/hello.zig`
- Test function signatures and compile-time validation
- Run with: `zig build test` or `make test-zig`

### Node.js Integration Tests
- Located in `test/hello_test.js`
- Test the compiled N-API module functionality
- Include performance benchmarks and edge cases
- Run with: `node test/run_tests.js` or `make test-node`

## Running Tests

### All Tests
```zig
zig build test
```

## Adding New Tests

### Zig Tests
Add test functions to the relevant `.zig` files using the `test` keyword:

```zig
test "my test description" {
    // Test implementation
    try testing.expect(condition);
}
```

### Node.js Tests
Add test cases following the existing structure:

```javascript
describe('Feature Name', () => {
    it('should do something', () => {
        // Test implementation
        assert.strictEqual(actual, expected);
    });
});
```
