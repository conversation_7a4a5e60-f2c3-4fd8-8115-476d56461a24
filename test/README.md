# Tests for zig-napi

This directory contains tests for the zig-napi project.

## Test Structure

### Zig Unit Tests
- Located in `examples/hello.zig`
- Test function signatures and compile-time validation
- Run with: `zig build test` or `make test-zig`

### Node.js Integration Tests
- Located in `test/hello_test.js`
- Test the compiled N-API module functionality
- Include performance benchmarks and edge cases
- Run with: `node test/run_tests.js` or `make test-node`

## Running Tests

### All Tests
```bash
make test
```

### Individual Test Suites
```bash
# Zig unit tests only
make test-zig

# Node.js integration tests only  
make test-node
```

### Manual Test Execution
```bash
# Build the project first
make build

# Run Zig tests
zig build test

# Run Node.js tests
node test/run_tests.js

# Run basic integration test
node index.js
```

## Test Categories

### Unit Tests (Zig)
- Function signature validation
- Compile-time checks
- Type safety verification

### Integration Tests (Node.js)
- Module loading and exports
- Function return values
- Error handling
- Performance benchmarks
- Concurrent execution

## Requirements

- Zig 0.14.1+
- Node.js (any version, but 18+ recommended for built-in test runner)
- Compiled addon (`hello.node`) must exist in `zig-out/lib/`

## Adding New Tests

### Zig Tests
Add test functions to the relevant `.zig` files using the `test` keyword:

```zig
test "my test description" {
    // Test implementation
    try testing.expect(condition);
}
```

### Node.js Tests
Add test cases to `test/hello_test.js` following the existing structure:

```javascript
describe('Feature Name', () => {
    it('should do something', () => {
        // Test implementation
        assert.strictEqual(actual, expected);
    });
});
```
