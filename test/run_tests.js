#!/usr/bin/env node

const { spawn } = require('child_process');
const path = require('path');
const fs = require('fs');

// Check if Node.js supports the test runner (Node.js 18+)
const nodeVersion = process.version;
const majorVersion = parseInt(nodeVersion.slice(1).split('.')[0]);

console.log(`Running tests with Node.js ${nodeVersion}`);

if (majorVersion >= 18) {
    // Use Node.js built-in test runner
    console.log('Using Node.js built-in test runner...');
    const testProcess = spawn('node', ['--test', 'test/hello_test.js'], {
        stdio: 'inherit',
        cwd: path.join(__dirname, '..')
    });

    testProcess.on('close', (code) => {
        if (code === 0) {
            console.log('✅ All Node.js tests passed!');
        } else {
            console.error('❌ Some Node.js tests failed!');
            process.exit(code);
        }
    });

    testProcess.on('error', (err) => {
        console.error('Failed to start test process:', err);
        process.exit(1);
    });
} else {
    // Fallback for older Node.js versions - run the test file directly
    console.log('Node.js version < 18, running tests directly...');
    try {
        require('./hello_test.js');
        console.log('✅ All Node.js tests passed!');
    } catch (error) {
        console.error('❌ Test failed:', error.message);
        process.exit(1);
    }
}
